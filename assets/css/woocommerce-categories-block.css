/**
 * Frontend styles for WooCommerce Categories Block
 */

.glowess-woocommerce-categories-block {
    margin: 20px 0;
}

/* Category Section */
.category-section {
    margin-bottom: 40px;
    border-bottom: 1px solid #eee;
    padding-bottom: 30px;
}

.category-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

/* Category Header */
.category-header {
    margin-bottom: 20px;
    text-align: center;
}

.category-title {
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 10px 0;
    color: #333;
}

.category-title a {
    text-decoration: none;
    color: inherit;
    transition: color 0.3s ease;
}

.category-title a:hover {
    color: #0073aa;
}

.category-count {
    font-size: 16px;
    color: #666;
    font-weight: 400;
    margin-left: 8px;
}

.category-description {
    font-size: 16px;
    color: #666;
    margin: 10px 0 0 0;
    line-height: 1.6;
}

/* Products Layout - Grid */
.category-products.layout-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

/* Products Layout - List */
.category-products.layout-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 20px;
}

.category-products.layout-list .product-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 1px solid #eee;
    border-radius: 8px;
    transition: box-shadow 0.3s ease;
}

.category-products.layout-list .product-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Product Item */
.product-item {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid #eee;
}

.category-products.layout-grid .product-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.product-link {
    display: block;
    text-decoration: none;
    color: inherit;
    height: 100%;
}

/* Product Image */
.product-image {
    position: relative;
    overflow: hidden;
    background: #f8f8f8;
}

.category-products.layout-grid .product-image {
    aspect-ratio: 1;
}

.category-products.layout-list .product-image {
    width: 80px;
    height: 80px;
    flex-shrink: 0;
    margin-right: 15px;
    border-radius: 6px;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-item:hover .product-image img {
    transform: scale(1.05);
}

/* Product Info */
.product-info {
    padding: 15px;
}

.category-products.layout-list .product-info {
    padding: 0;
    flex: 1;
}

.product-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #333;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-price {
    font-size: 18px;
    font-weight: 700;
    color: #0073aa;
}

.product-price .woocommerce-Price-amount {
    font-weight: inherit;
}

.product-price del {
    color: #999;
    font-weight: 400;
    margin-right: 8px;
}

/* View All Products */
.view-all-products {
    grid-column: 1 / -1;
    text-align: center;
    margin-top: 20px;
}

.category-products.layout-list .view-all-products {
    margin-top: 15px;
}

.view-all-link {
    display: inline-block;
    padding: 12px 24px;
    background: #0073aa;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 600;
    transition: background-color 0.3s ease;
}

.view-all-link:hover {
    background: #005a87;
    color: white;
}

/* No Products */
.no-products {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 40px 20px;
    background: #f9f9f9;
    border-radius: 8px;
}

/* Error States */
.glowess-error,
.glowess-no-categories {
    text-align: center;
    padding: 40px 20px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 8px;
    color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
    .category-products.layout-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }
    
    .category-title {
        font-size: 24px;
    }
    
    .category-products.layout-list .product-item {
        flex-direction: column;
        text-align: center;
    }
    
    .category-products.layout-list .product-image {
        width: 100%;
        height: 200px;
        margin-right: 0;
        margin-bottom: 15px;
    }
    
    .category-products.layout-list .product-info {
        padding: 0 15px 15px 15px;
    }
}

@media (max-width: 480px) {
    .category-products.layout-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 10px;
    }

    .category-title {
        font-size: 20px;
    }

    .product-info {
        padding: 10px;
    }

    .product-title {
        font-size: 14px;
    }

    .product-price {
        font-size: 16px;
    }
}

/* Animations */
.category-section {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.category-section.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.product-item {
    transition: transform 0.3s ease, box-shadow 0.3s ease, opacity 0.3s ease;
}

.product-item.hovered {
    transform: translateY(-5px);
}

/* Loading states */
.category-products.loading {
    opacity: 0.7;
    pointer-events: none;
}

.category-products.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Lazy loading */
img.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

img.lazy.loaded {
    opacity: 1;
}
