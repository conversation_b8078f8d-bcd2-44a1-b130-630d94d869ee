/**
 * Frontend JavaScript for WooCommerce Categories Block
 */

(function($) {
    'use strict';

    /**
     * WooCommerce Categories Block Handler
     */
    var GlowessWooCategoriesBlock = {
        
        /**
         * Initialize
         */
        init: function() {
            this.bindEvents();
            this.initLazyLoading();
            this.initAnimations();
        },
        
        /**
         * Bind events
         */
        bindEvents: function() {
            // Ürün hover efektleri
            $(document).on('mouseenter', '.glowess-woocommerce-categories-block .product-item', this.onProductHover);
            $(document).on('mouseleave', '.glowess-woocommerce-categories-block .product-item', this.onProductLeave);
            
            // Kategori başlığı tıklama
            $(document).on('click', '.category-title a', this.onCategoryClick);
            
            // View all products tıklama
            $(document).on('click', '.view-all-link', this.onViewAllClick);
            
            // Responsive grid ayarları
            $(window).on('resize', this.debounce(this.handleResize, 250));
        },
        
        /**
         * Product hover effect
         */
        onProductHover: function(e) {
            var $product = $(this);
            var $image = $product.find('.product-image img');
            
            // Hover animasyonu
            $product.addClass('hovered');
            
            // Eğer ürünün galeri görseli varsa değiştir
            if ($image.length && $image.data('hover-src')) {
                $image.attr('src', $image.data('hover-src'));
            }
        },
        
        /**
         * Product leave effect
         */
        onProductLeave: function(e) {
            var $product = $(this);
            var $image = $product.find('.product-image img');
            
            // Hover animasyonunu kaldır
            $product.removeClass('hovered');
            
            // Orijinal görsele geri dön
            if ($image.length && $image.data('original-src')) {
                $image.attr('src', $image.data('original-src'));
            }
        },
        
        /**
         * Category click tracking
         */
        onCategoryClick: function(e) {
            var $link = $(this);
            var categoryName = $link.text().trim();
            
            // Analytics tracking (eğer varsa)
            if (typeof gtag !== 'undefined') {
                gtag('event', 'category_click', {
                    'category_name': categoryName,
                    'event_category': 'woocommerce_categories_block'
                });
            }
        },
        
        /**
         * View all products click tracking
         */
        onViewAllClick: function(e) {
            var $link = $(this);
            var $categorySection = $link.closest('.category-section');
            var categoryId = $categorySection.data('category-id');
            
            // Analytics tracking (eğer varsa)
            if (typeof gtag !== 'undefined') {
                gtag('event', 'view_all_products', {
                    'category_id': categoryId,
                    'event_category': 'woocommerce_categories_block'
                });
            }
        },
        
        /**
         * Initialize lazy loading for images
         */
        initLazyLoading: function() {
            if ('IntersectionObserver' in window) {
                var imageObserver = new IntersectionObserver(function(entries, observer) {
                    entries.forEach(function(entry) {
                        if (entry.isIntersecting) {
                            var img = entry.target;
                            if (img.dataset.src) {
                                img.src = img.dataset.src;
                                img.classList.remove('lazy');
                                imageObserver.unobserve(img);
                            }
                        }
                    });
                });
                
                $('.glowess-woocommerce-categories-block img.lazy').each(function() {
                    imageObserver.observe(this);
                });
            }
        },
        
        /**
         * Initialize scroll animations
         */
        initAnimations: function() {
            if ('IntersectionObserver' in window) {
                var animationObserver = new IntersectionObserver(function(entries) {
                    entries.forEach(function(entry) {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('animate-in');
                        }
                    });
                }, {
                    threshold: 0.1,
                    rootMargin: '0px 0px -50px 0px'
                });
                
                $('.category-section').each(function() {
                    animationObserver.observe(this);
                });
            }
        },
        
        /**
         * Handle window resize
         */
        handleResize: function() {
            // Grid yeniden hesaplama
            $('.category-products.layout-grid').each(function() {
                var $grid = $(this);
                var containerWidth = $grid.width();
                var itemMinWidth = 250;
                var gap = 20;
                
                // Mobil cihazlarda minimum genişliği azalt
                if ($(window).width() <= 768) {
                    itemMinWidth = 200;
                    gap = 15;
                }
                if ($(window).width() <= 480) {
                    itemMinWidth = 150;
                    gap = 10;
                }
                
                var columns = Math.floor((containerWidth + gap) / (itemMinWidth + gap));
                columns = Math.max(1, Math.min(columns, 4)); // 1-4 arası sınırla
                
                $grid.css('grid-template-columns', 'repeat(' + columns + ', 1fr)');
            });
        },
        
        /**
         * Debounce function
         */
        debounce: function(func, wait) {
            var timeout;
            return function executedFunction() {
                var context = this;
                var args = arguments;
                var later = function() {
                    timeout = null;
                    func.apply(context, args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },
        
        /**
         * Load more products for a category (AJAX)
         */
        loadMoreProducts: function(categoryId, offset, limit) {
            var $categorySection = $('.category-section[data-category-id="' + categoryId + '"]');
            var $productsContainer = $categorySection.find('.category-products');
            
            // Loading state
            $productsContainer.addClass('loading');
            
            $.ajax({
                url: glowess_categories_block.ajax_url,
                type: 'POST',
                data: {
                    action: 'glowess_load_more_category_products',
                    category_id: categoryId,
                    offset: offset,
                    limit: limit,
                    nonce: glowess_categories_block.nonce
                },
                success: function(response) {
                    if (response.success && response.data.products) {
                        // Yeni ürünleri ekle
                        var $newProducts = $(response.data.products);
                        $productsContainer.append($newProducts);
                        
                        // Animasyonları başlat
                        $newProducts.addClass('animate-in');
                        
                        // Lazy loading'i yeniden başlat
                        GlowessWooCategoriesBlock.initLazyLoading();
                    }
                },
                error: function() {
                    console.log('Ürünler yüklenirken hata oluştu.');
                },
                complete: function() {
                    $productsContainer.removeClass('loading');
                }
            });
        }
    };
    
    /**
     * Initialize when document is ready
     */
    $(document).ready(function() {
        GlowessWooCategoriesBlock.init();
    });
    
    // Global erişim için
    window.GlowessWooCategoriesBlock = GlowessWooCategoriesBlock;
    
})(jQuery);
