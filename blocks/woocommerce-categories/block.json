{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 2, "name": "glowess/woocommerce-categories", "version": "1.0.0", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "widgets", "icon": "grid-view", "description": "WooCommerce kategorilerini ve ürünlerini gösterir", "keywords": ["woocommerce", "categories", "products", "kate<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "textdomain": "glowess-city-ecommerce", "supports": {"html": false, "align": ["wide", "full"], "spacing": {"margin": true, "padding": true}}, "attributes": {"showEmptyCategories": {"type": "boolean", "default": false}, "productsPerCategory": {"type": "number", "default": 4}, "showProductPrice": {"type": "boolean", "default": true}, "showProductImage": {"type": "boolean", "default": true}, "categoryLayout": {"type": "string", "default": "grid", "enum": ["grid", "list"]}, "productLayout": {"type": "string", "default": "grid", "enum": ["grid", "list"]}, "hideCategories": {"type": "array", "default": []}, "onlyCategories": {"type": "array", "default": []}}, "example": {"attributes": {"productsPerCategory": 3, "showProductPrice": true, "showProductImage": true}}}