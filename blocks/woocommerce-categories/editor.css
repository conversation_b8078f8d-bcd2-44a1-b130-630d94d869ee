/**
 * Editor styles for WooCommerce Categories Block
 */

.glowess-woocommerce-categories-block-editor {
    border: 2px dashed #ddd;
    padding: 20px;
    text-align: center;
    background: #f9f9f9;
    border-radius: 4px;
}

.glowess-block-preview {
    max-width: 500px;
    margin: 0 auto;
}

.glowess-block-preview .block-icon {
    margin-bottom: 15px;
}

.glowess-block-preview .block-icon .dashicons {
    font-size: 48px;
    color: #666;
    width: 48px;
    height: 48px;
}

.glowess-block-preview h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 18px;
}

.glowess-block-preview .block-settings {
    text-align: left;
    background: white;
    padding: 15px;
    border-radius: 4px;
    margin: 15px 0;
    border: 1px solid #ddd;
}

.glowess-block-preview .block-settings p {
    margin: 8px 0;
    font-size: 13px;
    color: #555;
}

.glowess-block-preview .categories-preview {
    text-align: left;
    background: white;
    padding: 15px;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.glowess-block-preview .categories-preview ul {
    margin: 10px 0 0 0;
    padding-left: 20px;
}

.glowess-block-preview .categories-preview li {
    margin: 5px 0;
    font-size: 13px;
    color: #666;
}

/* Inspector Controls Styling */
.components-panel__body .components-base-control__label {
    font-weight: 600;
    margin-bottom: 8px;
}

.components-panel__body .components-checkbox-control__label {
    font-size: 13px;
}

.components-panel__body .button-secondary {
    margin-top: 10px;
    font-size: 12px;
}
