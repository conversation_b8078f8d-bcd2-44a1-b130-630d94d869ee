/**
 * WordPress dependencies
 */
const { registerBlockType } = wp.blocks;
const { __ } = wp.i18n;
const { 
    InspectorControls,
    useBlockProps 
} = wp.blockEditor;
const { 
    PanelBody,
    ToggleControl,
    RangeControl,
    SelectControl,
    CheckboxControl
} = wp.components;
const { Fragment, useState, useEffect } = wp.element;

/**
 * Block registration
 */
registerBlockType('glowess/woocommerce-categories', {
    edit: EditComponent,
    save: () => null, // Server-side rendering
});

/**
 * Edit component
 */
function EditComponent({ attributes, setAttributes }) {
    const {
        showEmptyCategories,
        productsPerCategory,
        showProductPrice,
        showProductImage,
        categoryLayout,
        productLayout,
        hideCategories,
        onlyCategories
    } = attributes;

    const [categories, setCategories] = useState([]);
    const [loading, setLoading] = useState(true);

    const blockProps = useBlockProps({
        className: 'glowess-woocommerce-categories-block-editor'
    });

    // <PERSON><PERSON><PERSON>i yükle
    useEffect(() => {
        wp.apiFetch({
            path: '/wp/v2/product_cat?per_page=100&hide_empty=' + (!showEmptyCategories)
        }).then((cats) => {
            setCategories(cats);
            setLoading(false);
        }).catch(() => {
            setLoading(false);
        });
    }, [showEmptyCategories]);

    return (
        <Fragment>
            <InspectorControls>
                <PanelBody title={__('Genel Ayarlar', 'glowess-city-ecommerce')}>
                    <ToggleControl
                        label={__('Boş kategorileri göster', 'glowess-city-ecommerce')}
                        checked={showEmptyCategories}
                        onChange={(value) => setAttributes({ showEmptyCategories: value })}
                    />
                    
                    <RangeControl
                        label={__('Kategori başına ürün sayısı', 'glowess-city-ecommerce')}
                        value={productsPerCategory}
                        onChange={(value) => setAttributes({ productsPerCategory: value })}
                        min={1}
                        max={12}
                    />
                </PanelBody>

                <PanelBody title={__('Görünüm Ayarları', 'glowess-city-ecommerce')}>
                    <ToggleControl
                        label={__('Ürün fiyatlarını göster', 'glowess-city-ecommerce')}
                        checked={showProductPrice}
                        onChange={(value) => setAttributes({ showProductPrice: value })}
                    />
                    
                    <ToggleControl
                        label={__('Ürün görsellerini göster', 'glowess-city-ecommerce')}
                        checked={showProductImage}
                        onChange={(value) => setAttributes({ showProductImage: value })}
                    />
                    
                    <SelectControl
                        label={__('Kategori düzeni', 'glowess-city-ecommerce')}
                        value={categoryLayout}
                        options={[
                            { label: __('Grid', 'glowess-city-ecommerce'), value: 'grid' },
                            { label: __('Liste', 'glowess-city-ecommerce'), value: 'list' }
                        ]}
                        onChange={(value) => setAttributes({ categoryLayout: value })}
                    />
                    
                    <SelectControl
                        label={__('Ürün düzeni', 'glowess-city-ecommerce')}
                        value={productLayout}
                        options={[
                            { label: __('Grid', 'glowess-city-ecommerce'), value: 'grid' },
                            { label: __('Liste', 'glowess-city-ecommerce'), value: 'list' }
                        ]}
                        onChange={(value) => setAttributes({ productLayout: value })}
                    />
                </PanelBody>

                <PanelBody title={__('Kategori Filtreleme', 'glowess-city-ecommerce')} initialOpen={false}>
                    <p>{__('Gösterilecek kategorileri seçin:', 'glowess-city-ecommerce')}</p>
                    {categories.map((category) => (
                        <CheckboxControl
                            key={category.id}
                            label={`${category.name} (${category.count})`}
                            checked={onlyCategories.length === 0 || onlyCategories.includes(category.id)}
                            onChange={(checked) => {
                                if (checked) {
                                    // Kategoriyi ekle
                                    if (onlyCategories.length === 0) {
                                        // İlk seçim - sadece bu kategoriyi seç
                                        setAttributes({ onlyCategories: [category.id] });
                                    } else {
                                        // Mevcut seçimlere ekle
                                        setAttributes({ 
                                            onlyCategories: [...onlyCategories, category.id] 
                                        });
                                    }
                                } else {
                                    // Kategoriyi çıkar
                                    const newCategories = onlyCategories.filter(id => id !== category.id);
                                    setAttributes({ onlyCategories: newCategories });
                                }
                            }}
                        />
                    ))}
                    
                    {onlyCategories.length > 0 && (
                        <button 
                            className="button button-secondary"
                            onClick={() => setAttributes({ onlyCategories: [] })}
                        >
                            {__('Tüm kategorileri göster', 'glowess-city-ecommerce')}
                        </button>
                    )}
                </PanelBody>
            </InspectorControls>

            <div {...blockProps}>
                <div className="glowess-block-preview">
                    <div className="block-icon">
                        <span className="dashicons dashicons-grid-view"></span>
                    </div>
                    <h3>{__('WooCommerce Kategoriler', 'glowess-city-ecommerce')}</h3>
                    <div className="block-settings">
                        <p>
                            <strong>{__('Kategori başına ürün:', 'glowess-city-ecommerce')}</strong> {productsPerCategory}
                        </p>
                        <p>
                            <strong>{__('Düzen:', 'glowess-city-ecommerce')}</strong> {categoryLayout} / {productLayout}
                        </p>
                        <p>
                            <strong>{__('Fiyat göster:', 'glowess-city-ecommerce')}</strong> {showProductPrice ? __('Evet', 'glowess-city-ecommerce') : __('Hayır', 'glowess-city-ecommerce')}
                        </p>
                        <p>
                            <strong>{__('Görsel göster:', 'glowess-city-ecommerce')}</strong> {showProductImage ? __('Evet', 'glowess-city-ecommerce') : __('Hayır', 'glowess-city-ecommerce')}
                        </p>
                        {onlyCategories.length > 0 && (
                            <p>
                                <strong>{__('Seçili kategoriler:', 'glowess-city-ecommerce')}</strong> {onlyCategories.length}
                            </p>
                        )}
                    </div>
                    
                    {loading && (
                        <p>{__('Kategoriler yükleniyor...', 'glowess-city-ecommerce')}</p>
                    )}
                    
                    {!loading && categories.length === 0 && (
                        <p>{__('Kategori bulunamadı.', 'glowess-city-ecommerce')}</p>
                    )}
                    
                    {!loading && categories.length > 0 && (
                        <div className="categories-preview">
                            <p>
                                {__('Toplam kategoriler:', 'glowess-city-ecommerce')} {categories.length}
                            </p>
                            <ul>
                                {categories.slice(0, 5).map((category) => (
                                    <li key={category.id}>
                                        {category.name} ({category.count} ürün)
                                    </li>
                                ))}
                                {categories.length > 5 && (
                                    <li>... ve {categories.length - 5} kategori daha</li>
                                )}
                            </ul>
                        </div>
                    )}
                </div>
            </div>
        </Fragment>
    );
}
