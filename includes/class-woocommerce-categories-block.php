<?php
/**
 * WooCommerce Categories Block
 * 
 * WordPress blok sistemi için WooCommerce kategorilerini ve ürünlerini gösteren özel blok
 */

if (!defined('ABSPATH')) {
    exit;
}

class Glowess_WooCommerce_Categories_Block {

    /**
     * Single instance
     */
    private static $instance = null;

    /**
     * Get instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        add_action('init', array($this, 'register_block'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_assets'));
        add_action('enqueue_block_editor_assets', array($this, 'enqueue_editor_assets'));

        // AJAX handlers
        add_action('wp_ajax_glowess_load_more_category_products', array($this, 'ajax_load_more_products'));
        add_action('wp_ajax_nopriv_glowess_load_more_category_products', array($this, 'ajax_load_more_products'));
    }
    
    /**
     * Register the block
     */
    public function register_block() {
        // Blok zaten kayıtlı mı kontrol et
        if (WP_Block_Type_Registry::get_instance()->is_registered('glowess/city-woo-categories-showcase')) {
            return; // Zaten kayıtlı, tekrar kaydetme
        }

        // Debug: Dosya yolunu kontrol et
        $block_path = GLOWESS_CITY_ECOMMERCE_PLUGIN_DIR . 'blocks/woocommerce-categories';

        if (!file_exists($block_path . '/block.json')) {
            error_log('Glowess: block.json dosyası bulunamadı: ' . $block_path . '/block.json');
            return;
        }

        // Block.json dosyasını kullanarak blok kaydı
        $result = register_block_type(
            $block_path,
            array(
                'render_callback' => array($this, 'render_block'),
            )
        );

        if (!$result) {
            error_log('Glowess: Blok kaydı başarısız oldu');
        } else {
            error_log('Glowess: Kategori Vitrini blok başarıyla kaydedildi');
        }
    }
    
    /**
     * Enqueue frontend assets
     */
    public function enqueue_frontend_assets() {
        if (has_block('glowess/city-woo-categories-showcase')) {
            wp_enqueue_style(
                'glowess-woocommerce-categories-block',
                GLOWESS_CITY_ECOMMERCE_PLUGIN_URL . 'assets/css/woocommerce-categories-block.css',
                array(),
                GLOWESS_CITY_ECOMMERCE_VERSION
            );

            wp_enqueue_script(
                'glowess-woocommerce-categories-block',
                GLOWESS_CITY_ECOMMERCE_PLUGIN_URL . 'assets/js/woocommerce-categories-block.js',
                array('jquery'),
                GLOWESS_CITY_ECOMMERCE_VERSION,
                true
            );

            // Localize script with AJAX data
            wp_localize_script(
                'glowess-woocommerce-categories-block',
                'glowess_categories_block',
                array(
                    'ajax_url' => admin_url('admin-ajax.php'),
                    'nonce' => wp_create_nonce('glowess_categories_block_nonce'),
                    'strings' => array(
                        'loading' => __('Yükleniyor...', 'glowess-city-ecommerce'),
                        'error' => __('Bir hata oluştu.', 'glowess-city-ecommerce'),
                        'no_more_products' => __('Daha fazla ürün yok.', 'glowess-city-ecommerce'),
                    )
                )
            );
        }
    }
    
    /**
     * Enqueue editor assets
     */
    public function enqueue_editor_assets() {
        wp_enqueue_script(
            'glowess-woocommerce-categories-block-editor',
            GLOWESS_CITY_ECOMMERCE_PLUGIN_URL . 'blocks/woocommerce-categories/index.js',
            array('wp-blocks', 'wp-element', 'wp-editor', 'wp-components', 'wp-i18n'),
            GLOWESS_CITY_ECOMMERCE_VERSION
        );
        
        wp_enqueue_style(
            'glowess-woocommerce-categories-block-editor',
            GLOWESS_CITY_ECOMMERCE_PLUGIN_URL . 'blocks/woocommerce-categories/editor.css',
            array('wp-edit-blocks'),
            GLOWESS_CITY_ECOMMERCE_VERSION
        );
    }
    
    /**
     * Render the block
     */
    public function render_block($attributes, $content) {
        // WooCommerce kontrolü
        if (!class_exists('WooCommerce')) {
            return '<div class="glowess-error">' . __('WooCommerce gerekli!', 'glowess-city-ecommerce') . '</div>';
        }
        
        // Varsayılan ayarlar
        $attributes = wp_parse_args($attributes, array(
            'showEmptyCategories' => false,
            'productsPerCategory' => 4,
            'showProductPrice' => true,
            'showProductImage' => true,
            'categoryLayout' => 'grid',
            'productLayout' => 'grid',
            'hideCategories' => array(),
            'onlyCategories' => array(),
        ));
        
        // Kategorileri al
        $categories = $this->get_woocommerce_categories($attributes);
        
        if (empty($categories)) {
            return '<div class="glowess-no-categories">' . __('Kategori bulunamadı.', 'glowess-city-ecommerce') . '</div>';
        }
        
        // HTML çıktısını oluştur
        ob_start();
        ?>
        <div class="glowess-woocommerce-categories-block layout-<?php echo esc_attr($attributes['categoryLayout']); ?>">
            <?php foreach ($categories as $category): ?>
                <div class="category-section" data-category-id="<?php echo esc_attr($category->term_id); ?>">
                    <div class="category-header">
                        <h3 class="category-title">
                            <a href="<?php echo esc_url(get_term_link($category)); ?>">
                                <?php echo esc_html($category->name); ?>
                            </a>
                            <span class="category-count">(<?php echo $category->count; ?>)</span>
                        </h3>
                        <?php if (!empty($category->description)): ?>
                            <p class="category-description"><?php echo esc_html($category->description); ?></p>
                        <?php endif; ?>
                    </div>
                    
                    <div class="category-products layout-<?php echo esc_attr($attributes['productLayout']); ?>">
                        <?php 
                        $products = $this->get_category_products($category->term_id, $attributes['productsPerCategory']);
                        if (!empty($products)):
                        ?>
                            <?php foreach ($products as $product): ?>
                                <?php $this->render_product_item($product, $attributes); ?>
                            <?php endforeach; ?>
                            
                            <?php if ($category->count > $attributes['productsPerCategory']): ?>
                                <div class="view-all-products">
                                    <a href="<?php echo esc_url(get_term_link($category)); ?>" class="view-all-link">
                                        <?php printf(__('Tüm %s ürünü gör', 'glowess-city-ecommerce'), $category->count); ?>
                                    </a>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <p class="no-products"><?php _e('Bu kategoride ürün bulunamadı.', 'glowess-city-ecommerce'); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        <?php
        
        return ob_get_clean();
    }
    
    /**
     * Get WooCommerce categories
     */
    private function get_woocommerce_categories($attributes) {
        $args = array(
            'taxonomy' => 'product_cat',
            'hide_empty' => !$attributes['showEmptyCategories'],
            'orderby' => 'name',
            'order' => 'ASC',
        );
        
        // Sadece belirli kategorileri göster
        if (!empty($attributes['onlyCategories'])) {
            $args['include'] = $attributes['onlyCategories'];
        }
        
        // Belirli kategorileri gizle
        if (!empty($attributes['hideCategories'])) {
            $args['exclude'] = $attributes['hideCategories'];
        }
        
        $categories = get_terms($args);
        
        if (is_wp_error($categories)) {
            return array();
        }
        
        return $categories;
    }
    
    /**
     * Get products for a category
     */
    private function get_category_products($category_id, $limit = 4) {
        $args = array(
            'post_type' => 'product',
            'post_status' => 'publish',
            'posts_per_page' => $limit,
            'meta_query' => array(
                array(
                    'key' => '_visibility',
                    'value' => array('catalog', 'visible'),
                    'compare' => 'IN'
                )
            ),
            'tax_query' => array(
                array(
                    'taxonomy' => 'product_cat',
                    'field' => 'term_id',
                    'terms' => $category_id,
                )
            )
        );
        
        $products = get_posts($args);
        
        return $products;
    }

    /**
     * AJAX handler for loading more products
     */
    public function ajax_load_more_products() {
        // Nonce kontrolü
        if (!wp_verify_nonce($_POST['nonce'], 'glowess_categories_block_nonce')) {
            wp_send_json_error('Invalid nonce');
        }

        $category_id = intval($_POST['category_id']);
        $offset = intval($_POST['offset']);
        $limit = intval($_POST['limit']);

        if (!$category_id || $limit <= 0) {
            wp_send_json_error('Invalid parameters');
        }

        // Ürünleri al
        $args = array(
            'post_type' => 'product',
            'post_status' => 'publish',
            'posts_per_page' => $limit,
            'offset' => $offset,
            'meta_query' => array(
                array(
                    'key' => '_visibility',
                    'value' => array('catalog', 'visible'),
                    'compare' => 'IN'
                )
            ),
            'tax_query' => array(
                array(
                    'taxonomy' => 'product_cat',
                    'field' => 'term_id',
                    'terms' => $category_id,
                )
            )
        );

        $products = get_posts($args);

        if (empty($products)) {
            wp_send_json_error('No more products found');
        }

        // HTML oluştur
        ob_start();
        foreach ($products as $product) {
            $this->render_product_item($product, array(
                'showProductPrice' => true,
                'showProductImage' => true,
                'productLayout' => 'grid'
            ));
        }
        $html = ob_get_clean();

        wp_send_json_success(array(
            'products' => $html,
            'count' => count($products)
        ));
    }

    /**
     * Render single product item
     */
    private function render_product_item($product, $attributes) {
        $wc_product = wc_get_product($product->ID);
        if (!$wc_product) return;
        ?>
        <div class="product-item">
            <a href="<?php echo esc_url(get_permalink($product->ID)); ?>" class="product-link">
                <?php if ($attributes['showProductImage'] && has_post_thumbnail($product->ID)): ?>
                    <div class="product-image">
                        <?php echo get_the_post_thumbnail($product->ID, 'woocommerce_thumbnail'); ?>
                    </div>
                <?php endif; ?>

                <div class="product-info">
                    <h4 class="product-title"><?php echo esc_html($product->post_title); ?></h4>

                    <?php if ($attributes['showProductPrice']): ?>
                        <div class="product-price">
                            <?php echo $wc_product->get_price_html(); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </a>
        </div>
        <?php
    }
}

// Initialize the block (singleton pattern)
Glowess_WooCommerce_Categories_Block::get_instance();
